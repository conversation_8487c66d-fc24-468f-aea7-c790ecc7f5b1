# Morph Cloud Experiments

This repository contains examples and experiments with **Morph Cloud's Infinibranch technology** - a revolutionary sandbox environment for code execution designed specifically for software agents.

## What is Morph Cloud?

**Morph Cloud** provides sandbox environments that can:
- **Snapshot, branch, and restore entire computational environments in under 250ms**
- **Perfect state preservation** - running processes continue exactly where they left off
- **Instant parallel branching** - create multiple copies of running environments with near-zero overhead
- **Sub-second boot times** instead of traditional 2-3 minute VM startup times

This is particularly powerful for **AI agents** because they can:
- Explore thousands of parallel execution paths efficiently
- Recover from failures instantly by reverting to previous states
- Scale horizontally with minimal resource overhead
- Debug issues by accessing the exact state where problems occurred

## Quick Start

### Prerequisites

1. **Sign up** for a free account at [cloud.morph.so](https://cloud.morph.so)
2. **Get your API key** from the dashboard
3. **Install the required packages**:
   ```bash
   pip install morphcloud python-dotenv
   ```
4. **Set your API key** (choose one option):
   - **Option A**: Create .env file directly:
     ```bash
     echo "MORPH_API_KEY='your-api-key-here'" > .env
     ```
   - **Option B**: Export environment variable:
     ```bash
     export MORPH_API_KEY='your-api-key-here'
     ```

### Option 1: Automated Setup

Run the setup script to install dependencies:

```bash
chmod +x setup.sh
./setup.sh
```

### Option 2: Manual Setup

```bash
pip install -r requirements.txt
```

## Examples

### 1. Python Example (Recommended)

The main example demonstrates perfect state preservation:

```bash
python3 morph_example.py
```

**What it does:**
- Creates a VM instance with a background counter process
- Takes a snapshot of the running instance (including the running process!)
- Creates a new instance from the snapshot
- Verifies that the counter process continues exactly where it left off

### 2. CLI Example

For those who prefer command-line tools:

```bash
chmod +x morph_cli_example.sh
./morph_cli_example.sh
```

## Key Concepts Demonstrated

### Perfect State Preservation
Unlike traditional VMs that restart processes, Morph Cloud preserves:
- Running processes and their exact state
- Memory contents
- File system state
- Network connections
- Everything needed for seamless continuation

### Infinibranch Technology
- **Snapshot**: Capture the complete state of a running environment
- **Branch**: Create multiple parallel instances from a single snapshot
- **Restore**: Return to any previous state instantly

### Use Cases for AI Agents

1. **Parallel Exploration**: Agents can explore multiple solution paths simultaneously
2. **Failure Recovery**: Instantly revert to working states when errors occur
3. **A/B Testing**: Test different approaches in parallel environments
4. **Debugging**: Access the exact state where issues occurred
5. **Scaling**: Horizontally scale agent workloads with minimal overhead

## Real-World Applications

- **CI/CD Debugging**: Access the exact state of any failure instantly
- **Agent Development**: Enable agents to explore thousands of parallel paths
- **Development Environments**: Create staging environments that mirror production perfectly
- **Testing at Scale**: Generate and validate thousands of test cases efficiently

## Learn More

- [Morph Cloud Documentation](https://cloud.morph.so/docs/developers)
- [API Reference](https://cloud.morph.so/docs/api-reference)
- [Python SDK](https://github.com/morph-labs/morph-python-sdk)
- [TypeScript SDK](https://github.com/morph-labs/morph-typescript-sdk)
- [Blog](https://morph.so/blog/)
- [Twitter/X](https://x.com/morph_labs)

## Files in This Repository

- `morph_example.py` - Main Python example demonstrating state preservation
- `morph_cli_example.sh` - CLI-based example using bash
- `setup.sh` - Automated setup script
- `requirements.txt` - Python dependencies
- `agent.py` - Your custom agent experiments (empty, ready for your code!)

## Next Steps

1. Run the examples to see Infinibranch technology in action
2. Explore the [documentation](https://cloud.morph.so/docs/developers) for advanced features
3. Start building your own AI agents with perfect state management
4. Check out real-world case studies in the [blog](https://morph.so/blog/)

---

**Ready to revolutionize your AI agent development?** Start with the examples above and experience the future of cloud computing! 🚀